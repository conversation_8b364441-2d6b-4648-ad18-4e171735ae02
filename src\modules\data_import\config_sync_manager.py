#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置同步管理器

功能说明:
- 管理字段映射配置的持久化和同步
- 实时保存、版本管理、冲突解决
- 批量更新、缓存优化
- 配置变更审计日志

功能函数:
- save_mapping(): 保存字段映射配置
- load_mapping(): 加载字段映射配置
- update_mapping(): 更新字段映射配置
- backup_mappings(): 备份配置
- restore_mappings(): 恢复配置

创建时间: 2025-06-23
作者: 开发团队
"""

import json
import os
import shutil
import threading
import time
import platform
try:
    import fcntl
except ImportError:
    fcntl = None

from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

from src.utils.log_config import setup_logger
from src.core.event_bus import Event


class ConfigSyncManager:
    """配置同步管理器

    提供字段映射配置的持久化和同步功能
    通过ArchitectureFactory进行依赖注入，确保全局配置一致性
    🆕 [新架构] 移除单例模式，完全依赖依赖注入
    """

    def __init__(self, config_path: Optional[str] = None, event_bus=None):
        """初始化配置同步管理器

        Args:
            config_path: 配置文件路径
            event_bus: 事件总线实例（用于发布配置变更事件）
        """

        self.logger = setup_logger(__name__)
        self.event_bus = event_bus

        # 配置文件路径
        self.config_path = Path(config_path or "state/data/field_mappings.json")
        self.backup_dir = Path("state/data/backups")
        self.change_log_path = Path("state/data/mapping_changes.log")

        # 内存缓存
        self.memory_cache = {}
        self.mapping_cache = {}  # 添加缺失的mapping_cache属性
        self.cache_lock = threading.RLock()

        # 缓存统计
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "updates": 0
        }

        # 初始化配置
        self._initialize_config()

        self.logger.info("🆕 [新架构] 配置同步管理器初始化完成（依赖注入）")

    def _initialize_config(self):
        """初始化配置目录和文件，支持备份恢复"""

        # 创建必要的目录
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        self.backup_dir.mkdir(parents=True, exist_ok=True)

        # 使用跨平台的文件锁防止并发初始化问题
        lock_path = f"{str(self.config_path)}.init.lock"
        try:
            # 跨平台文件锁实现
            with open(lock_path, 'w') as lock_file:
                # 检查fcntl是否可用（仅在Unix系统可用）
                if fcntl is not None:
                    try:
                        fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                        self.logger.debug("使用fcntl文件锁")
                    except OSError:
                        # 锁已被占用：改为短时自旋且让出时间片，避免固定sleep阻塞
                        import time as _t
                        start = _t.time()
                        while _t.time() - start < 0.12:
                            _t.sleep(0)  # 让出时间片，避免CPU空转
                        self.logger.debug("文件锁被占用，已短暂等待后继续")
                else:
                    # Windows系统使用简单的延迟检查
                    if Path(lock_path).exists():
                        # 避免固定阻塞，使用让出时间片
                        import time as _t
                        _t.sleep(0)
                    self.logger.debug("Windows系统，使用延迟锁机制")

                lock_file.write(f"Config initialization lock - {datetime.now()}")
                lock_file.flush()

                self._do_config_initialization()

        except (OSError, IOError) as e:
            # 如果无法获取锁，等待一小段时间后重试
            self.logger.warning(f"配置初始化锁定失败，使用备用初始化: {e}")
            # 避免固定sleep，快速让出时间片
            try:
                import time as _t
                _t.sleep(0)
            except Exception:
                pass
            self._do_config_initialization()
        finally:
            # 清理锁文件
            try:
                if Path(lock_path).exists():
                    Path(lock_path).unlink()
            except:
                pass

    def _do_config_initialization(self):
        """执行实际的配置初始化"""
        # 双重检查文件是否存在
        if not self.config_path.exists():
            # 首先尝试从备份恢复
            if self._try_restore_from_backup():
                self.logger.info("已从备份恢复配置文件")
                return

            # 如果没有备份，创建最小默认配置
            self._create_minimal_default_config()
            self.logger.info("🔧 [配置修复] 创建了完整的默认配置，包含基本字段映射和模板")
        else:
            # 验证现有配置，确保包含字段模板
            self._ensure_field_templates_exist()
            self.logger.info(f"使用已存在的配置文件: {str(self.config_path)}")

    def _try_restore_from_backup(self) -> bool:
        """尝试从最近的备份文件恢复配置"""
        try:
            # 查找最近的备份文件
            backup_pattern = f"{str(self.config_path)}.backup_*"
            backup_files = sorted(Path().glob(backup_pattern), reverse=True)

            for backup_file in backup_files[:3]:  # 只检查最近3个备份
                if backup_file.exists() and backup_file.stat().st_size > 1000:  # 文件大于1KB
                    shutil.copy2(backup_file, str(self.config_path))
                    self.logger.info(f"从备份恢复配置: {backup_file}")
                    return True

            # 检查state/data/backups目录中的备份
            backup_dir_path = Path(self.backup_dir)
            if backup_dir_path.exists():
                backup_files = sorted(backup_dir_path.glob("field_mappings_*.json"), reverse=True)
                for backup_file in backup_files[:3]:
                    if backup_file.stat().st_size > 1000:
                        shutil.copy2(backup_file, str(self.config_path))
                        self.logger.info(f"从备份目录恢复配置: {backup_file}")
                        return True

        except Exception as e:
            self.logger.warning(f"备份恢复失败: {e}")

        return False

    def _create_minimal_default_config(self):
        """创建最小默认配置，不覆盖现有映射"""
        minimal_config = {
            "version": "2.0",
            "last_updated": datetime.now().isoformat(),
            "global_settings": {
                "auto_generate_mappings": True,
                "enable_smart_suggestions": True,
                "save_edit_history": True,
                "preserve_chinese_headers": True
            },
            "table_mappings": self._get_default_table_mappings(),  # 🔧 [配置修复] 初始化基本字段映射
            "field_templates": self._get_default_field_templates(),
            "user_preferences": {
                "default_field_patterns": {},
                "recent_edits": [],
                "favorite_mappings": []
            }
        }
        self._save_config_file(minimal_config)

    def _get_default_field_templates(self) -> Dict[str, Dict[str, str]]:
        """获取默认字段模板"""
        return {
            "离休人员工资表": {
                "sequence_number": "序号",
                "employee_id": "人员代码",
                "employee_name": "姓名",
                "department": "部门名称",
                "basic_retirement_salary": "基本离休费",
                "balance_allowance": "结余津贴",
                "living_allowance": "生活补贴",
                "housing_allowance": "住房补贴",
                "property_allowance": "物业补贴",
                "retirement_allowance": "离休补贴",
                "nursing_fee": "护理费",
                "one_time_living_allowance": "增发一次性生活补贴",
                "supplement": "补发",
                "total": "合计",
                "advance": "借支",
                "remarks": "备注"
            },
            "退休人员工资表": {
                "sequence_number": "序号",
                "employee_id": "人员代码",
                "employee_name": "姓名",
                "department": "部门名称",
                "employee_type_code": "人员类别代码",
                "basic_retirement_salary": "基本退休费",
                "allowance": "津贴",
                "balance_allowance": "结余津贴",
                "retirement_living_allowance": "离退休生活补贴",
                "nursing_fee": "护理费",
                "property_allowance": "物业补贴",
                "housing_allowance": "住房补贴",
                "salary_advance": "增资预付",
                "adjustment_2016": "2016待遇调整",
                "adjustment_2017": "2017待遇调整",
                "adjustment_2018": "2018待遇调整",
                "adjustment_2019": "2019待遇调整",
                "adjustment_2020": "2020待遇调整",
                "adjustment_2021": "2021待遇调整",
                "adjustment_2022": "2022待遇调整",
                "adjustment_2023": "2023待遇调整",
                "supplement": "补发",
                "advance": "借支",
                "total_salary": "应发工资",
                "provident_fund": "公积",
                "insurance_deduction": "保险扣款",
                "remarks": "备注"
            },
            "全部在职人员工资表": {
                "sequence_number": "序号",
                "employee_id": "工号",
                "employee_name": "姓名",
                "department": "部门名称",
                "employee_type": "人员类别",
                "employee_type_code": "人员类别代码",
                "position_salary_2025": "2025年岗位工资",
                "grade_salary_2025": "2025年薪级工资",
                "allowance": "津贴",
                "balance_allowance": "结余津贴",
                "basic_performance_2025": "2025年基础性绩效",
                "health_fee": "卫生费",
                "transport_allowance": "交通补贴",
                "property_allowance": "物业补贴",
                "housing_allowance": "住房补贴",
                "car_allowance": "车补",
                "communication_allowance": "通讯补贴",
                "performance_bonus_2025": "2025年奖励性绩效预发",
                "supplement": "补发",
                "advance": "借支",
                "total_salary": "应发工资",
                "provident_fund_2025": "2025公积金",
                "pension_insurance": "代扣代存养老保险"
            },
            "A岗职工": {
                "sequence_number": "序号",
                "employee_id": "工号",
                "employee_name": "姓名",
                "department": "部门名称",
                "employee_type": "人员类别",
                "employee_type_code": "人员类别代码",
                "position_salary_2025": "2025年岗位工资",
                "seniority_salary_2025": "2025年校龄工资",
                "allowance": "津贴",
                "balance_allowance": "结余津贴",
                "basic_performance_2025": "2025年基础性绩效",
                "health_fee": "卫生费",
                "living_allowance_2025": "2025年生活补贴",
                "car_allowance": "车补",
                "performance_bonus_2025": "2025年奖励性绩效预发",
                "supplement": "补发",
                "advance": "借支",
                "total_salary": "应发工资",
                "provident_fund_2025": "2025公积金",
                "insurance_deduction": "保险扣款",
                "pension_insurance": "代扣代存养老保险"
            }
        }

    def _get_default_table_mappings(self) -> Dict[str, Dict[str, str]]:
        """
        获取默认表格映射配置

        🔧 [配置修复] 提供基本的表格字段映射，避免启动时映射为空
        """
        return {
            "salary_data_2025_07_active_employees": {
                "工号": "employee_id",
                "姓名": "employee_name",
                "部门名称": "department",
                "人员类别": "employee_type",
                "人员类别代码": "employee_type_code",
                "2025年岗位工资": "position_salary_2025",
                "2025年薪级工资": "grade_salary_2025",
                "津贴": "allowance",
                "结余津贴": "balance_allowance",
                "应发工资": "total_salary"
            },
            "active_employees": {
                "工号": "employee_id",
                "姓名": "employee_name",
                "部门名称": "department",
                "人员类别": "employee_type",
                "人员类别代码": "employee_type_code"
            }
        }

    def _ensure_field_templates_exist(self):
        """确保配置文件中存在字段模板"""
        try:
            config = self._load_config_file()
            if "field_templates" not in config or not config["field_templates"]:
                config["field_templates"] = self._get_default_field_templates()
                self._save_config_file(config)
                self.logger.info("添加默认字段模板到现有配置文件")
        except Exception as e:
            self.logger.error(f"确保字段模板存在失败: {e}")

    def get_template_mapping(self, table_type: str) -> Optional[Dict[str, str]]:
        """根据表类型获取模板映射

        Args:
            table_type: 表类型（如"离休人员工资表"、"退休人员工资表"等）

        Returns:
            Optional[Dict[str, str]]: 字段映射模板，如果不存在返回None
        """
        try:
            config = self._load_config_file()
            templates = config.get("field_templates", {})
            return templates.get(table_type)
        except Exception as e:
            self.logger.error(f"获取模板映射失败: {e}")
            return None

    def apply_template_to_table(self, table_name: str, table_type: str) -> bool:
        """将模板映射应用到指定表

        Args:
            table_name: 表名
            table_type: 表类型

        Returns:
            bool: 是否应用成功
        """
        try:
            template_mapping = self.get_template_mapping(table_type)
            if not template_mapping:
                self.logger.warning(f"未找到表类型 {table_type} 的模板")
                return False

            # 应用模板映射
            metadata = {
                "source": "template",
                "table_type": table_type,
                "auto_generated": True,
                "user_modified": False
            }

            success = self.save_mapping(table_name, template_mapping, metadata)
            if success:
                self.logger.info(f"成功将 {table_type} 模板应用到表 {table_name}")

            return success

        except Exception as e:
            self.logger.error(f"应用模板映射失败: {e}")
            return False

    def save_mapping(self, table_name: str, mapping: Dict[str, str],
                     metadata: Optional[Dict[str, Any]] = None) -> bool:
        """保存字段映射配置

        Args:
            table_name: 表名
            mapping: 字段映射
            metadata: 元数据

        Returns:
            bool: 是否保存成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()

                # 准备映射数据
                mapping_data = {
                    "metadata": {
                        "created_at": datetime.now().isoformat(),
                        "last_modified": datetime.now().isoformat(),
                        "auto_generated": metadata.get("auto_generated", True) if metadata else True,
                        "user_modified": metadata.get("user_modified", False) if metadata else False
                    },
                    "field_mappings": mapping,
                    "edit_history": []
                }

                # 如果已存在，保留历史记录
                if table_name in config.get("table_mappings", {}):
                    existing_data = config["table_mappings"][table_name]
                    mapping_data["edit_history"] = existing_data.get("edit_history", [])
                    mapping_data["metadata"]["created_at"] = existing_data.get("metadata", {}).get("created_at", mapping_data["metadata"]["created_at"])

                # 更新配置
                config["table_mappings"][table_name] = mapping_data
                config["last_updated"] = datetime.now().isoformat()

                # 保存到文件
                success = self._save_config_file(config)

                if success:
                    # 更新内存缓存
                    self.memory_cache[table_name] = mapping.copy()
                    self.cache_stats["updates"] += 1

                    # 记录变更日志
                    self._log_mapping_change(table_name, "save", mapping)

                    # 发布配置变更事件
                    self._publish_config_change_event(table_name, mapping, "save")

                    self.logger.info(f"字段映射保存成功: {table_name}")

                    try:
                        # 通知导入窗口记录本地保存时间（若在上下文中）
                        from PyQt5.QtWidgets import QApplication
                        app = QApplication.instance()
                        if app is not None:
                            # 尝试从活动窗口链上找到导入窗口并设置时间戳
                            for w in app.topLevelWidgets():
                                if hasattr(w, '_last_local_save_ts'):
                                    import time as _t
                                    w._last_local_save_ts = _t.time()
                    except Exception:
                        pass

                return success

        except Exception as e:
            self.logger.error(f"保存字段映射失败 {table_name}: {e}")
            return False

    def load_mapping(self, table_name: str) -> Optional[Dict[str, Any]]:
        """
        加载字段映射配置（增强版：支持模糊匹配和向后兼容）

        Args:
            table_name: 表名

        Returns:
            Optional[Dict[str, Any]]: 映射配置，包含完整字段配置
        """
        try:
            # 1. 精确匹配
            if table_name in self.mapping_cache:
                cached_data = self.mapping_cache[table_name]
                return self._extract_field_configs(cached_data, table_name)

            # 2. 从文件精确加载
            try:
                # 从文件精确加载（修复：读取 table_mappings 分支）
                config_data = self._load_config_file()
                table_mappings = config_data.get("table_mappings", {})
                if table_name in table_mappings:
                    mapping_data = table_mappings[table_name]
                    # 缓存完整表配置数据，便于后续复用
                    self.mapping_cache[table_name] = mapping_data
                    return self._extract_field_configs(mapping_data, table_name)
            except Exception as e:
                self.logger.debug(f"从文件加载配置失败: {e}")


            # 3. 模糊匹配：查找同Sheet的历史配置
            sheet_name = self._extract_sheet_name_from_table(table_name)
            if sheet_name:
                self.logger.info(f" [模糊匹配] 尝试查找Sheet '{sheet_name}' 的历史配置")

                # 搜索所有缓存中的表名
                for cached_table_name, cached_data in self.mapping_cache.items():
                    if self._is_same_sheet_config(cached_table_name, sheet_name):
                        self.logger.info(f" [模糊匹配] 找到匹配配置: {cached_table_name}")
                        return self._extract_field_configs(cached_data, cached_table_name)

                # 搜索文件中的所有配置
                fuzzy_configs = self._search_sheet_configs_in_file(sheet_name)
                if fuzzy_configs:
                    # 使用最新的配置
                    latest_config = max(fuzzy_configs, key=lambda x: x.get('timestamp', 0))
                    self.logger.info(f" [模糊匹配] 使用最新配置: {latest_config.get('table_name', 'unknown')}")
                    return self._extract_field_configs(latest_config, table_name)

            self.logger.info(f" [配置查找] 未找到配置: {table_name}")
            return None

        except Exception as e:
            self.logger.error(f"加载映射配置失败: {table_name}, 错误: {e}")
            return None

    def _extract_field_configs(self, cached_data: Dict[str, Any], table_name: str) -> Optional[Dict[str, Any]]:
        """🔧 [方案3实施] 从缓存数据中提取字段配置 - 修复配置读取优先级问题"""
        try:
            # 🔧 [方案3] 优先从edit_history获取最新配置，而不是field_configs
            latest_configs = self._extract_latest_configs_from_history(cached_data, table_name)
            if latest_configs:
                self.logger.info(f"🔧 [方案3] 从edit_history提取最新配置: {table_name}, 字段数: {len(latest_configs)}")
                return latest_configs

            # 备用方案：使用field_configs（但要检查数据一致性）
            if 'field_configs' in cached_data and isinstance(cached_data['field_configs'], dict):
                configs = cached_data['field_configs']
                # 键名一致化：如果存在 db_field 则映射为 target_field
                normalized: Dict[str, Any] = {}
                for excel_field, cfg in configs.items():
                    if isinstance(cfg, dict):
                        cfg = cfg.copy()
                        if 'target_field' not in cfg and 'db_field' in cfg:
                            cfg['target_field'] = cfg.get('db_field')
                        # 保障必要键存在
                        cfg.setdefault('field_type', cfg.get('type', ''))
                        cfg.setdefault('data_type', cfg.get('sql_type', ''))
                        cfg.setdefault('is_required', cfg.get('required', False))
                        normalized[excel_field] = cfg
                    else:
                        # 非预期结构，跳过或包裹
                        normalized[excel_field] = {
                            'target_field': str(cfg),
                            'field_type': '',
                            'data_type': '',
                            'is_required': False
                        }
                self.logger.warning(f"⚠️ [方案3] 使用field_configs备用配置: {table_name}, 字段数: {len(normalized)}")
                return normalized

            # 兼容格式1：field_mappings（excel_field -> db_field）
            if 'field_mappings' in cached_data and isinstance(cached_data['field_mappings'], dict):
                self.logger.info(f" [配置提取] 从 field_mappings 构造字段配置: {table_name}")
                field_configs = {}
                for excel_field, db_field in cached_data['field_mappings'].items():
                    field_configs[excel_field] = {
                        'target_field': db_field,
                        'field_type': '',
                        'data_type': '',
                        'is_required': False
                    }
                return field_configs

            # 兼容格式2：mapping（历史旧键名）
            if 'mapping' in cached_data and isinstance(cached_data['mapping'], dict):
                self.logger.info(f" [配置提取] 转换旧格式映射配置(mapping): {table_name}")
                field_configs = {}
                for excel_field, db_field in cached_data['mapping'].items():
                    field_configs[excel_field] = {
                        'target_field': db_field,
                        'field_type': '',
                        'data_type': '',
                        'is_required': False
                    }
                return field_configs

            return None

        except Exception as e:
            self.logger.error(f"提取字段配置失败: {table_name}, 错误: {e}")
            return None

    def _extract_latest_configs_from_history(self, cached_data: Dict[str, Any], table_name: str) -> Optional[Dict[str, Any]]:
        """🔧 [方案3实施] 从edit_history中提取最新的字段配置

        Args:
            cached_data: 缓存的表数据
            table_name: 表名

        Returns:
            Optional[Dict[str, Any]]: 最新的字段配置，如果没有则返回None
        """
        try:
            if 'edit_history' not in cached_data or not isinstance(cached_data['edit_history'], list):
                self.logger.debug(f"🔧 [方案3] 表 {table_name} 没有edit_history")
                return None

            edit_history = cached_data['edit_history']
            if not edit_history:
                self.logger.debug(f"🔧 [方案3] 表 {table_name} edit_history为空")
                return None

            # 按字段分组，获取每个字段的最新配置
            field_latest_configs = {}

            for entry in edit_history:
                if not isinstance(entry, dict):
                    continue

                field_name = entry.get('field')
                action = entry.get('action')
                config = entry.get('config')
                timestamp = entry.get('timestamp')

                if not field_name or action != 'field_config_update' or not config:
                    continue

                # 检查是否是更新的配置
                if field_name not in field_latest_configs:
                    field_latest_configs[field_name] = {
                        'config': config,
                        'timestamp': timestamp
                    }
                else:
                    # 🔧 [方案3修复] 改进时间戳比较逻辑
                    current_timestamp = field_latest_configs[field_name]['timestamp']
                    if timestamp and (not current_timestamp or self._compare_timestamps(timestamp, current_timestamp) > 0):
                        field_latest_configs[field_name] = {
                            'config': config,
                            'timestamp': timestamp
                        }

            if not field_latest_configs:
                self.logger.debug(f"🔧 [方案3] 表 {table_name} edit_history中没有有效的字段配置")
                return None

            # 转换为标准格式
            normalized_configs = {}
            for field_name, latest_data in field_latest_configs.items():
                config = latest_data['config']
                normalized_configs[field_name] = {
                    'target_field': config.get('target_field', field_name),
                    'field_type': config.get('field_type', ''),
                    'data_type': config.get('data_type', ''),
                    'is_required': config.get('is_required', False),
                    'last_modified': config.get('last_modified', 0)
                }

            self.logger.info(f"🔧 [方案3] 从edit_history提取到 {len(normalized_configs)} 个字段的最新配置")

            # 🔧 [方案3修复] 增强调试信息：显示提取的字段类型
            for field_name, config in normalized_configs.items():
                field_type = config.get('field_type', 'N/A')
                self.logger.info(f"🔧 [方案3修复] 字段 '{field_name}' 最新类型: {field_type}")
                # 特别关注保险相关字段
                if '保险' in field_name or '代扣' in field_name:
                    self.logger.warning(f"🔧 [方案3修复] 重点字段 '{field_name}' 类型: {field_type}")

            return normalized_configs

        except Exception as e:
            self.logger.error(f"🔧 [方案3] 从edit_history提取配置失败: {e}")
            import traceback
            self.logger.debug(f"🔧 [方案3] 详细错误: {traceback.format_exc()}")
            return None

    def _compare_timestamps(self, timestamp1: str, timestamp2: str) -> int:
        """🔧 [方案3修复] 比较两个时间戳，支持ISO格式和Unix时间戳

        Args:
            timestamp1: 第一个时间戳
            timestamp2: 第二个时间戳

        Returns:
            int: 1 如果timestamp1更新，-1 如果timestamp2更新，0 如果相等
        """
        try:
            import datetime

            def parse_timestamp(ts):
                if isinstance(ts, (int, float)):
                    return ts
                elif isinstance(ts, str):
                    if 'T' in ts:  # ISO格式
                        dt = datetime.datetime.fromisoformat(ts.replace('Z', '+00:00'))
                        return dt.timestamp()
                    else:
                        return float(ts)
                return 0

            ts1 = parse_timestamp(timestamp1)
            ts2 = parse_timestamp(timestamp2)

            if ts1 > ts2:
                return 1
            elif ts1 < ts2:
                return -1
            else:
                return 0

        except Exception as e:
            self.logger.warning(f"🔧 [方案3修复] 时间戳比较失败: {e}, 使用字符串比较")
            # 备用：字符串比较
            if timestamp1 > timestamp2:
                return 1
            elif timestamp1 < timestamp2:
                return -1
            else:
                return 0

    def _extract_sheet_name_from_table(self, table_name: str) -> Optional[str]:
        """从表名中提取Sheet名称"""
        try:
            # 表名格式：mapping_config_{hash}_{sheet_name} 或 mapping_config_{sheet_name}
            parts = table_name.split('_')
            if len(parts) >= 3:
                # 去掉 mapping_config 前缀
                if parts[0] == 'mapping' and parts[1] == 'config':
                    if len(parts) == 3:
                        # mapping_config_{sheet_name}
                        return parts[2]
                    elif len(parts) >= 4:
                        # mapping_config_{hash}_{sheet_name} 或 mapping_config_{sheet_name}_{timestamp}
                        # 检查第3部分是否是哈希（8位十六进制）
                        if len(parts[2]) == 8 and all(c in '0123456789abcdef' for c in parts[2].lower()):
                            # 哈希格式，Sheet名称从第4部分开始
                            return '_'.join(parts[3:])
                        else:
                            # 非哈希格式，Sheet名称从第3部分开始（可能包含时间戳）
                            sheet_parts = parts[2:]
                            # 移除可能的时间戳后缀
                            if len(sheet_parts) > 1 and len(sheet_parts[-1]) >= 8:
                                # 检查最后一部分是否像时间戳
                                last_part = sheet_parts[-1]
                                if last_part.isdigit() or ('_' in last_part and all(p.isdigit() for p in last_part.split('_'))):
                                    return '_'.join(sheet_parts[:-1])
                            return '_'.join(sheet_parts)

            return None

        except Exception as e:
            self.logger.error(f"提取Sheet名称失败: {table_name}, 错误: {e}")
            return None

    def _is_same_sheet_config(self, table_name: str, target_sheet: str) -> bool:
        """检查表名是否属于同一个Sheet"""
        try:
            sheet_name = self._extract_sheet_name_from_table(table_name)
            return sheet_name == target_sheet
        except:
            return False

    def _search_sheet_configs_in_file(self, sheet_name: str) -> List[Dict[str, Any]]:
        """在配置文件中搜索指定Sheet的所有配置"""
        try:
            if not self.config_path.exists():
                return []

            # 修复：从标准结构 table_mappings 中搜索
            config = self._load_config_file()
            table_mappings = config.get('table_mappings', {})

            def _to_ts(meta: Dict[str, Any]) -> float:
                # 尝试从 last_modified/created_at 解析时间戳
                from datetime import datetime
                for key in ('last_modified', 'created_at', 'timestamp'):
                    v = meta.get(key)
                    if not v:
                        continue
                    try:
                        if isinstance(v, (int, float)):
                            return float(v)
                        # ISO 格式
                        return datetime.fromisoformat(v).timestamp()
                    except Exception:
                        continue
                return 0.0

            matching_configs = []
            for tbl_name, cfg in table_mappings.items():
                if self._is_same_sheet_config(tbl_name, sheet_name):
                    config_copy = cfg.copy()
                    config_copy['table_name'] = tbl_name
                    meta = cfg.get('metadata', {}) if isinstance(cfg, dict) else {}
                    config_copy['timestamp'] = _to_ts(meta)
                    matching_configs.append(config_copy)

            self.logger.info(f"💾 [文件搜索] 找到 {len(matching_configs)} 个Sheet '{sheet_name}' 的配置")
            return matching_configs

        except Exception as e:
            self.logger.error(f"搜索Sheet配置失败: {sheet_name}, 错误: {e}")
            return []

    def update_mapping(self, table_name: str, field_name: str, new_display_name: str) -> bool:
        """更新单个字段映射

        Args:
            table_name: 表名
            field_name: 字段名
            new_display_name: 新的显示名称

        Returns:
            bool: 是否更新成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()

                # 检查表是否存在，如果不存在则创建
                if table_name not in config.get("table_mappings", {}):
                    self.logger.info(f"表 {table_name} 配置不存在，创建新的字段映射配置")
                    config.setdefault("table_mappings", {})[table_name] = {
                        "metadata": {
                            "created_at": datetime.now().isoformat(),
                            "last_modified": datetime.now().isoformat(),
                            "auto_generated": False,
                            "user_modified": True
                        },
                        "field_mappings": {},
                        "edit_history": []
                    }

                # 获取当前映射
                table_data = config["table_mappings"][table_name]
                field_mappings = table_data.get("field_mappings", {})

                # 检查字段是否存在，如果不存在则创建新字段
                old_display_name = field_mappings.get(field_name, field_name)
                if field_name not in field_mappings:
                    self.logger.info(f"字段 {field_name} 在表 {table_name} 中不存在，创建新字段映射")
                    field_mappings[field_name] = field_name  # 初始值为原字段名

                # 记录历史变更
                edit_history = table_data.get("edit_history", [])
                edit_history.append({
                    "timestamp": datetime.now().isoformat(),
                    "field": field_name,
                    "old_value": old_display_name,
                    "new_value": new_display_name,
                    "user_action": True
                })

                # 更新映射
                field_mappings[field_name] = new_display_name
                table_data["field_mappings"] = field_mappings
                table_data["edit_history"] = edit_history
                table_data["metadata"]["last_modified"] = datetime.now().isoformat()
                table_data["metadata"]["user_modified"] = True

                # 更新配置
                config["table_mappings"][table_name] = table_data
                config["last_updated"] = datetime.now().isoformat()

                # 保存到文件
                success = self._save_config_file(config)

                if success:
                    # 更新内存缓存
                    if table_name in self.memory_cache:
                        self.memory_cache[table_name][field_name] = new_display_name

                    # 记录变更日志
                    self._log_field_change(table_name, field_name, old_display_name, new_display_name)

                    self.logger.info(f"字段映射更新成功: {table_name}.{field_name} -> {new_display_name}")

                return success

        except Exception as e:
            self.logger.error(f"更新字段映射失败: {e}")
            return False

    def update_single_field_mapping(self, table_name: str, db_field: str, display_name: str) -> bool:
        """更新单个字段的显示名称（新方法，按照解决方案设计）

        Args:
            table_name: 表名
            db_field: 数据库字段名
            display_name: 显示名称

        Returns:
            bool: 是否更新成功
        """
        try:
            config = self._load_config_file()

            if table_name not in config.get("table_mappings", {}):
                self.logger.error(f"表 {table_name} 的映射配置不存在")
                return False

            # 更新字段映射
            config["table_mappings"][table_name]["field_mappings"][db_field] = display_name

            # 更新元数据
            config["table_mappings"][table_name]["metadata"]["user_modified"] = True
            config["table_mappings"][table_name]["metadata"]["last_modified"] = datetime.now().isoformat()

            # 添加编辑历史
            if "edit_history" not in config["table_mappings"][table_name]:
                config["table_mappings"][table_name]["edit_history"] = []

            config["table_mappings"][table_name]["edit_history"].append({
                "timestamp": datetime.now().isoformat(),
                "field": db_field,
                "new_value": display_name,
                "action": "user_edit"
            })

            # 保存配置
            success = self._save_config_file(config)

            if success:
                # 更新内存缓存
                if table_name in self.memory_cache:
                    self.memory_cache[table_name][db_field] = display_name

                self.logger.info(f"字段映射更新成功: {table_name}.{db_field} -> {display_name}")

            return success

        except Exception as e:
            self.logger.error(f"更新单个字段映射失败: {e}")
            return False

    def save_complete_mapping(self, table_name: str, mapping_data: Dict[str, Any]) -> bool:
        """保存完整的字段映射配置（新方法，按照解决方案设计）

        Args:
            table_name: 表名
            mapping_data: 完整的映射数据，包含field_mappings、original_excel_headers、metadata等

        Returns:
            bool: 是否保存成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()

                # 确保必要的键存在
                if "table_mappings" not in config:
                    config["table_mappings"] = {}

                # 🔧 [P1-1修复] 统一字段映射格式
                if table_name.startswith("change_data"):
                    # 异动表：转换为与工资表一致的格式
                    field_mappings = mapping_data.get("field_mappings", {})
                    # 异动表保持中文字段名，但使用统一的扁平结构
                    unified_mapping = {}
                    for display_name, db_field in field_mappings.items():
                        # 异动表的db_field通常就是中文字段名
                        unified_mapping[display_name] = db_field

                    # 保存为统一格式（与工资表一致）
                    config["table_mappings"][table_name] = unified_mapping
                    self.logger.info(f"🔧 [P1-1修复] 异动表使用统一映射格式: {len(unified_mapping)}个字段")
                else:
                    # 工资表：保持原有格式
                    config["table_mappings"][table_name] = mapping_data

                config["last_updated"] = datetime.now().isoformat()

                # 保存到文件
                success = self._save_config_file(config)

                if success:
                    # 更新内存缓存
                    if table_name.startswith("change_data"):
                        # 异动表：缓存统一格式的映射
                        self.memory_cache[table_name] = unified_mapping.copy()
                    else:
                        # 工资表：缓存原有格式
                        field_mappings = mapping_data.get("field_mappings", mapping_data)
                        self.memory_cache[table_name] = field_mappings.copy()

                    self.cache_stats["updates"] += 1

                    # 🚀 [新架构] 发布配置变更事件，通知所有组件配置已更新
                    try:
                        if hasattr(self, 'event_bus') and self.event_bus:
                            self.event_bus.publish(Event(
                                event_type="config_updated",
                                data={
                                    "table_name": table_name,
                                    "update_type": "complete_mapping",
                                    "timestamp": datetime.now().isoformat(),
                                    "affected_fields": list(field_mappings.keys()) if field_mappings else []
                                }
                            ))
                            self.logger.info(f"🚀 [配置同步] 已发布配置变更事件: {table_name}")
                    except Exception as event_error:
                        self.logger.warning(f"🚀 [配置同步] 发布配置变更事件失败: {event_error}")

                    self.logger.info(f"完整字段映射保存成功: {table_name}")

                return success

        except Exception as e:
            self.logger.error(f"保存完整字段映射失败: {e}")
            return False

    def batch_update_mappings(self, updates: List[Dict[str, Any]]) -> bool:
        """批量更新字段映射

        Args:
            updates: 更新列表，每个元素包含 table_name, field_name, new_display_name

        Returns:
            bool: 是否更新成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()

                # 处理所有更新
                for update in updates:
                    table_name = update.get("table_name")
                    field_name = update.get("field_name")
                    new_display_name = update.get("new_display_name")

                    if not all([table_name, field_name, new_display_name]):
                        continue

                    # 更新单个字段
                    if table_name in config.get("table_mappings", {}):
                        table_data = config["table_mappings"][table_name]
                        field_mappings = table_data.get("field_mappings", {})

                        if field_name in field_mappings:
                            old_value = field_mappings[field_name]
                            field_mappings[field_name] = new_display_name

                            # 记录历史
                            edit_history = table_data.get("edit_history", [])
                            edit_history.append({
                                "timestamp": datetime.now().isoformat(),
                                "field": field_name,
                                "old_value": old_value,
                                "new_value": new_display_name,
                                "user_action": True
                            })

                            table_data["edit_history"] = edit_history
                            table_data["metadata"]["last_modified"] = datetime.now().isoformat()
                            table_data["metadata"]["user_modified"] = True

                # 更新全局配置
                config["last_updated"] = datetime.now().isoformat()

                # 保存到文件
                success = self._save_config_file(config)

                if success:
                    # 清除相关缓存
                    updated_tables = {update.get("table_name") for update in updates}
                    for table_name in updated_tables:
                        if table_name and table_name in self.memory_cache:
                            del self.memory_cache[table_name]

                    self.logger.info(f"批量更新完成，共处理 {len(updates)} 个字段")

                return success

        except Exception as e:
            self.logger.error(f"批量更新字段映射失败: {e}")
            return False

    def save_field_mapping(self, table_name: str, excel_field: str, field_config: Dict[str, Any]) -> bool:
        """保存单个字段的完整配置 - 支持即时保存机制

        Args:
            table_name: 表名
            excel_field: Excel字段名
            field_config: 字段配置字典，包含field_type, data_type, is_required, last_modified等

        Returns:
            bool: 是否保存成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()

                # 去抖：避免高频微小变动频繁落盘
                if not hasattr(self, '_last_save_ts'):
                    self._last_save_ts = 0.0
                import time as _t
                now = _t.time()
                min_interval = 0.6  # 600ms 最小间隔
                if now - self._last_save_ts < min_interval:
                    _t.sleep(min_interval - (now - self._last_save_ts))
                self._last_save_ts = _t.time()

                # 确保表映射存在
                if "table_mappings" not in config:
                    config["table_mappings"] = {}

                if table_name not in config["table_mappings"]:
                    config["table_mappings"][table_name] = {
                        "metadata": {
                            "created_at": datetime.now().isoformat(),
                            "last_modified": datetime.now().isoformat(),
                            "auto_generated": False,
                            "user_modified": True
                        },
                        "field_mappings": {},
                        "field_configs": {},  # 新增：存储字段完整配置
                        "edit_history": []
                    }

                table_data = config["table_mappings"][table_name]

                # 更新字段映射
                target_field = field_config.get('target_field', excel_field)
                table_data["field_mappings"][excel_field] = target_field

                # 保存字段完整配置
                if "field_configs" not in table_data:
                    table_data["field_configs"] = {}

                table_data["field_configs"][excel_field] = {
                    "field_type": field_config.get('field_type', ''),
                    "data_type": field_config.get('data_type', ''),
                    "is_required": field_config.get('is_required', False),
                    "last_modified": field_config.get('last_modified', time.time())
                }

                # 记录编辑历史
                if "edit_history" not in table_data:
                    table_data["edit_history"] = []

                table_data["edit_history"].append({
                    "timestamp": datetime.now().isoformat(),
                    "field": excel_field,
                    "action": "field_config_update",
                    "config": field_config.copy(),
                    "user_action": True
                })

                # 更新元数据
                table_data["metadata"]["last_modified"] = datetime.now().isoformat()
                table_data["metadata"]["user_modified"] = True

                # 更新全局配置
                config["last_updated"] = datetime.now().isoformat()

                # 保存到文件
                success = self._save_config_file(config)

                if success:
                    # 更新内存缓存
                    if table_name in self.memory_cache:
                        self.memory_cache[table_name][excel_field] = target_field

                    # 🔧 [缓存修复] 清除mapping_cache以确保下次加载时重新读取文件
                    with self.cache_lock:
                        if table_name in self.mapping_cache:
                            del self.mapping_cache[table_name]
                            self.logger.debug(f"🔧 [缓存修复] 已清除表 {table_name} 的mapping_cache")

                    self.logger.info(f"💾 [即时保存] 字段配置保存成功: {table_name}.{excel_field}")
                    return success
        except Exception as e:
            self.logger.error(f"💾 [即时保存] 保存字段配置失败: {e}")
            return False

    def batch_save_field_configs(self, table_name: str, field_configs: Dict[str, Dict[str, Any]]) -> bool:
        """批量保存多个字段的完整配置（单次落盘，避免高频IO）

        Args:
            table_name: 表名
            field_configs: { excel_field: {target_field, field_type, data_type, is_required, last_modified} }

        Returns:
            bool: 是否保存成功
        """
        try:
            with self.cache_lock:
                config = self._load_config_file()

                # 确保目标表结构存在
                if "table_mappings" not in config:
                    config["table_mappings"] = {}
                if table_name not in config["table_mappings"]:
                    config["table_mappings"][table_name] = {
                        "metadata": {
                            "created_at": datetime.now().isoformat(),
                            "last_modified": datetime.now().isoformat(),
                            "auto_generated": False,
                            "user_modified": True
                        },
                        "field_mappings": {},
                        "field_configs": {},
                        "edit_history": []
                    }

                table_data = config["table_mappings"][table_name]
                table_data.setdefault("field_mappings", {})
                table_data.setdefault("field_configs", {})
                table_data.setdefault("edit_history", [])

                # 批量更新字段
                for excel_field, fc in field_configs.items():
                    if not isinstance(fc, dict):
                        continue
                    target_field = fc.get('target_field', excel_field)
                    table_data["field_mappings"][excel_field] = target_field
                    table_data["field_configs"][excel_field] = {
                        "field_type": fc.get('field_type', ''),
                        "data_type": fc.get('data_type', ''),
                        "is_required": fc.get('is_required', False),
                        "last_modified": fc.get('last_modified', time.time())
                    }
                    table_data["edit_history"].append({
                        "timestamp": datetime.now().isoformat(),
                        "field": excel_field,
                        "action": "field_config_update",
                        "config": fc.copy(),
                        "user_action": False
                    })

                # 更新元数据与全局时间
                table_data["metadata"]["last_modified"] = datetime.now().isoformat()
                table_data["metadata"]["user_modified"] = True
                config["table_mappings"][table_name] = table_data
                config["last_updated"] = datetime.now().isoformat()

                # 单次写入
                success = self._save_config_file(config)

                if success:
                    # 更新内存缓存（仅字段映射的快捷缓存）
                    if table_name not in self.memory_cache:
                        self.memory_cache[table_name] = {}
                    for excel_field, fc in field_configs.items():
                        tf = fc.get('target_field', excel_field)
                        self.memory_cache[table_name][excel_field] = tf

                    # 清除mapping_cache对应项，确保下次从文件读取
                    if table_name in self.mapping_cache:
                        del self.mapping_cache[table_name]

                    # 发布配置变更事件（轻量）
                    try:
                        if hasattr(self, 'event_bus') and self.event_bus:
                            self.event_bus.publish(Event(
                                event_type="config_updated",
                                data={
                                    "table_name": table_name,
                                    "update_type": "batch_field_config_update",
                                    "timestamp": datetime.now().isoformat(),
                                    "affected_fields": list(field_configs.keys())
                                }
                            ))
                    except Exception:
                        pass

                    self.logger.info(f"💾 [批量保存] 表 {table_name} 保存字段数: {len(field_configs)}")

                return success
        except Exception as e:
            self.logger.error(f"💾 [批量保存] 失败: {e}")
            return False


    def backup_mappings(self) -> bool:
        """备份字段映射配置

        Returns:
            bool: 是否备份成功
        """
        try:
            if not self.config_path.exists():
                self.logger.warning("配置文件不存在，跳过备份")
                return True

            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"field_mappings_backup_{timestamp}.json"
            backup_path = Path(self.backup_dir) / backup_filename

            # 复制配置文件
            shutil.copy2(str(self.config_path), backup_path)

            # 清理旧备份（保留最近10个）
            self._cleanup_old_backups(max_backups=10)

            self.logger.info(f"配置备份成功: {backup_path}")
            return True

        except Exception as e:
            self.logger.error(f"备份配置失败: {e}")
            return False

    def restore_mappings(self, backup_filename: str) -> bool:
        """恢复字段映射配置

        Args:
            backup_filename: 备份文件名

        Returns:
            bool: 是否恢复成功
        """
        try:
            backup_path = Path(self.backup_dir) / backup_filename

            if not backup_path.exists():
                self.logger.error(f"备份文件不存在: {backup_path}")
                return False

            # 先备份当前配置
            if not self.backup_mappings():
                self.logger.warning("备份当前配置失败，但继续恢复操作")

            # 恢复配置
            shutil.copy2(backup_path, str(self.config_path))

            # 清除内存缓存
            with self.cache_lock:
                self.memory_cache.clear()

            self.logger.info(f"配置恢复成功: {backup_filename}")
            return True

        except Exception as e:
            self.logger.error(f"恢复配置失败: {e}")
            return False

    def _load_config_file(self) -> Dict[str, Any]:
        """加载配置文件

        Returns:
            Dict[str, Any]: 配置数据
        """
        try:
            with open(str(self.config_path), 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 确保必要的键存在
                if "table_mappings" not in config:
                    config["table_mappings"] = {}
                return config
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            # 返回默认结构
            return {
                "version": "2.0",
                "last_updated": datetime.now().isoformat(),
                "global_settings": {
                    "auto_generate_mappings": True,
                    "enable_smart_suggestions": True,
                    "save_edit_history": True
                },
                "table_mappings": {},
                "user_preferences": {
                    "default_field_patterns": {},
                    "recent_edits": [],
                    "favorite_mappings": []
                }
            }

    def _save_config_file(self, config: Dict[str, Any]) -> bool:
        # 写入前：开始监控静默窗口，避免触发风暴
        try:
            from src.core.config_monitor import get_config_monitor
            monitor = get_config_monitor(str(self.config_path))
            if monitor:
                monitor.begin_silence(3.5)
        except Exception:
            pass

        """保存配置文件

        Args:
            config: 配置数据

        Returns:
            bool: 是否保存成功
        """
        try:
            # 确保目录存在
            config_dir = self.config_path.parent
            config_dir.mkdir(parents=True, exist_ok=True)

            # 创建临时文件
            temp_path = f"{str(self.config_path)}.tmp"

            # 检查文件权限
            if self.config_path.exists():
                # 如果目标文件存在，检查是否可写
                if not os.access(str(self.config_path), os.W_OK):
                    self.logger.error(f"配置文件无写入权限: {str(self.config_path)}")
                    return False
            else:
                # 如果目标文件不存在，检查目录是否可写
                if not os.access(config_dir, os.W_OK):
                    self.logger.error(f"配置目录无写入权限: {config_dir}")
                    return False

            # 写入临时文件
            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            # 原子性替换 + 重试退避（Windows 可能被占用）
            import time as _t
            max_retries = 5
            delay = 0.05
            replaced = False
            for attempt in range(1, max_retries + 1):
                try:
                    os.replace(temp_path, str(self.config_path))
                    replaced = True
                    break
                except PermissionError as pe:
                    self.logger.warning(f"文件替换权限错误(第{attempt}次): {pe}")
                    _t.sleep(delay)
                    delay *= 2
                except Exception as e:
                    self.logger.warning(f"文件替换失败(第{attempt}次): {e}")
                    _t.sleep(delay)
                    delay *= 2

            # 写入后：结束监控静默窗口
            # 延迟结束静默窗口，确保至少越过一次监控巡检周期
            try:
                from PyQt5.QtCore import QTimer
                from src.core.config_monitor import get_config_monitor
                monitor = get_config_monitor(str(self.config_path))
                if monitor:
                    QTimer.singleShot(3200, lambda: monitor.end_silence())
            except Exception:
                pass
            if not replaced:
                # 兜底：尝试直接覆盖
                try:
                    with open(str(self.config_path), 'w', encoding='utf-8') as f:
                        json.dump(config, f, ensure_ascii=False, indent=2)
                    if Path(temp_path).exists():
                        Path(temp_path).unlink()
                except Exception as e2:
                    self.logger.error(f"直接覆盖也失败: {e2}")
                    return False

            return True

        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            # 清理临时文件
            temp_path = f"{str(self.config_path)}.tmp"
            try:
                if Path(temp_path).exists():
                    Path(temp_path).unlink()
            except:
                pass
            return False

    def _log_mapping_change(self, table_name: str, action: str, mapping: Dict[str, str]):
        """记录映射变更日志

        Args:
            table_name: 表名
            action: 操作类型
            mapping: 映射数据
        """
        try:
            change_record = {
                "timestamp": datetime.now().isoformat(),
                "table_name": table_name,
                "action": action,
                "field_count": len(mapping),
                "fields": list(mapping.keys())
            }

            with open(self.change_log_path, 'a', encoding='utf-8') as f:
                f.write(json.dumps(change_record, ensure_ascii=False) + "\n")

        except Exception as e:
            self.logger.warning(f"记录变更日志失败: {e}")

    def _log_field_change(self, table_name: str, field_name: str, old_value: str, new_value: str):
        """记录字段变更日志

        Args:
            table_name: 表名
            field_name: 字段名
            old_value: 旧值
            new_value: 新值
        """
        try:
            change_record = {
                "timestamp": datetime.now().isoformat(),
                "table_name": table_name,
                "action": "field_update",
                "field_name": field_name,
                "old_value": old_value,
                "new_value": new_value
            }

            with open(self.change_log_path, 'a', encoding='utf-8') as f:
                f.write(json.dumps(change_record, ensure_ascii=False) + "\n")

        except Exception as e:
            self.logger.warning(f"记录字段变更日志失败: {e}")

    def _cleanup_old_backups(self, max_backups: int = 10):
        """清理旧备份文件

        Args:
            max_backups: 最大保留备份数量
        """
        try:
            backup_files = list(Path(self.backup_dir).glob("field_mappings_backup_*.json"))
            backup_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)

            # 删除多余的备份
            for old_backup in backup_files[max_backups:]:
                old_backup.unlink()
                self.logger.debug(f"删除旧备份: {old_backup}")

        except Exception as e:
            self.logger.warning(f"清理旧备份失败: {e}")

    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息

        Returns:
            Dict[str, Any]: 缓存统计
        """
        with self.cache_lock:
            total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
            hit_rate = self.cache_stats["hits"] / total_requests if total_requests > 0 else 0

            return {
                "cache_size": len(self.memory_cache),
                "hit_rate": round(hit_rate, 3),
                "total_requests": total_requests,
                **self.cache_stats
            }

    def clear_cache(self):
        """清除内存缓存"""
        with self.cache_lock:
            self.memory_cache.clear()
            self.logger.info("内存缓存已清除")

    def get_mapping(self, table_name: str) -> Optional[Dict[str, str]]:
        """获取字段映射配置（load_mapping的别名方法）

        Args:
            table_name: 表名

        Returns:
            Optional[Dict[str, str]]: 字段映射，如果不存在返回None
        """
        return self.load_mapping(table_name)

    def get_all_table_names(self) -> List[str]:
        """获取所有表名列表

        Returns:
            List[str]: 表名列表
        """
        try:
            config = self._load_config_file()
            return list(config.get("table_mappings", {}).keys())
        except Exception as e:
            self.logger.error(f"获取表名列表失败: {e}")
            return []

    def save_user_header_preference(self, table_name: str, selected_fields: List[str]) -> bool:
        """保存用户表头显示偏好

        Args:
            table_name: 表名
            selected_fields: 用户选择的字段列表

        Returns:
            bool: 是否保存成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()

                # 确保user_preferences存在
                if 'user_preferences' not in config:
                    config['user_preferences'] = {
                        "default_field_patterns": {},
                        "recent_edits": [],
                        "favorite_mappings": [],
                        "header_preferences": {}
                    }

                if 'header_preferences' not in config['user_preferences']:
                    config['user_preferences']['header_preferences'] = {}

                # 保存表头偏好
                config['user_preferences']['header_preferences'][table_name] = {
                    "selected_fields": selected_fields,
                    "updated_at": datetime.now().isoformat(),
                    "field_count": len(selected_fields)
                }

                # 更新配置
                config["last_updated"] = datetime.now().isoformat()

                # 保存到文件
                success = self._save_config_file(config)

                if success:
                    # 记录变更日志
                    self._log_mapping_change(table_name, "save_header_preference", {
                        "selected_fields": selected_fields,
                        "field_count": len(selected_fields)
                    })

                    self.logger.info(f"用户表头偏好保存成功: {table_name}, {len(selected_fields)} 个字段")

                return success

        except Exception as e:
            self.logger.error(f"保存用户表头偏好失败 {table_name}: {e}")
            return False

    def get_user_header_preference(self, table_name: str) -> Optional[List[str]]:
        """获取用户表头显示偏好

        Args:
            table_name: 表名

        Returns:
            Optional[List[str]]: 用户选择的字段列表，如果不存在返回None
        """
        try:
            config = self._load_config_file()

            # 检查用户偏好是否存在
            user_prefs = config.get('user_preferences', {})
            header_prefs = user_prefs.get('header_preferences', {})

            if table_name in header_prefs:
                preference_data = header_prefs[table_name]
                if isinstance(preference_data, dict):
                    return preference_data.get('selected_fields', [])
                elif isinstance(preference_data, list):
                    # 兼容旧格式
                    return preference_data

            return None

        except Exception as e:
            self.logger.error(f"获取用户表头偏好失败 {table_name}: {e}")
            return None

    def remove_user_header_preference(self, table_name: str) -> bool:
        """删除用户表头显示偏好

        Args:
            table_name: 表名

        Returns:
            bool: 是否删除成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()

                # 检查偏好是否存在
                user_prefs = config.get('user_preferences', {})
                header_prefs = user_prefs.get('header_preferences', {})

                if table_name in header_prefs:
                    del header_prefs[table_name]

                    # 更新配置
                    config["last_updated"] = datetime.now().isoformat()

                    # 保存到文件
                    success = self._save_config_file(config)

                    if success:
                        self.logger.info(f"用户表头偏好删除成功: {table_name}")

                    return success
                else:
                    self.logger.debug(f"表 {table_name} 没有用户表头偏好，无需删除")
                    return True

        except Exception as e:
            self.logger.error(f"删除用户表头偏好失败 {table_name}: {e}")
            return False

    def get_all_user_header_preferences(self) -> Dict[str, List[str]]:
        """获取所有用户表头显示偏好

        Returns:
            Dict[str, List[str]]: 所有表的用户偏好 {表名: 字段列表}
        """
        try:
            config = self._load_config_file()

            user_prefs = config.get('user_preferences', {})
            header_prefs = user_prefs.get('header_preferences', {})

            result = {}
            for table_name, preference_data in header_prefs.items():
                if isinstance(preference_data, dict):
                    result[table_name] = preference_data.get('selected_fields', [])
                elif isinstance(preference_data, list):
                    # 兼容旧格式
                    result[table_name] = preference_data

            return result

        except Exception as e:
            self.logger.error(f"获取所有用户表头偏好失败: {e}")
            return {}

    def save_table_field_preference(self, table_name: str, selected_fields: List[str]) -> bool:
        """保存表级字段显示偏好（替代全局偏好）

        Args:
            table_name: 表名
            selected_fields: 用户选择的字段列表

        Returns:
            bool: 是否保存成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()

                # 确保table_preferences存在
                if 'table_preferences' not in config:
                    config['table_preferences'] = {}

                # 保存表级字段偏好
                config['table_preferences'][table_name] = {
                    "preferred_fields": selected_fields,
                    "updated_at": datetime.now().isoformat(),
                    "field_count": len(selected_fields)
                }

                # 更新配置
                config["last_updated"] = datetime.now().isoformat()

                # 保存到文件
                success = self._save_config_file(config)

                if success:
                    # 记录变更日志
                    self._log_mapping_change(table_name, "save_table_field_preference", {
                        "preferred_fields": selected_fields,
                        "field_count": len(selected_fields)
                    })

                    self.logger.info(f"表级字段偏好保存成功: {table_name}, {len(selected_fields)} 个字段")

                return success

        except Exception as e:
            self.logger.error(f"保存表级字段偏好失败 {table_name}: {e}")
            return False

    def get_table_field_preference(self, table_name: str) -> Optional[List[str]]:
        """获取表级字段显示偏好

        Args:
            table_name: 表名

        Returns:
            Optional[List[str]]: 用户选择的字段列表，如果不存在返回None
        """
        try:
            config = self._load_config_file()

            # 检查表级偏好是否存在
            table_prefs = config.get('table_preferences', {})

            if table_name in table_prefs:
                preference_data = table_prefs[table_name]
                if isinstance(preference_data, dict):
                    return preference_data.get('preferred_fields', [])
                elif isinstance(preference_data, list):
                    # 兼容旧格式
                    return preference_data

            return None

        except Exception as e:
            self.logger.error(f"获取表级字段偏好失败 {table_name}: {e}")
            return None

    def remove_table_field_preference(self, table_name: str) -> bool:
        """删除表级字段显示偏好

        Args:
            table_name: 表名

        Returns:
            bool: 是否删除成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()

                # 检查偏好是否存在
                table_prefs = config.get('table_preferences', {})

                if table_name in table_prefs:
                    del table_prefs[table_name]

                    # 更新配置
                    config["last_updated"] = datetime.now().isoformat()

                    # 保存到文件
                    success = self._save_config_file(config)

                    if success:
                        self.logger.info(f"表级字段偏好删除成功: {table_name}")

                    return success
                else:
                    self.logger.debug(f"表 {table_name} 没有字段偏好，无需删除")
                    return True

        except Exception as e:
            self.logger.error(f"删除表级字段偏好失败 {table_name}: {e}")
            return False

    def get_all_table_field_preferences(self) -> Dict[str, List[str]]:
        """获取所有表级字段显示偏好

        Returns:
            Dict[str, List[str]]: 所有表的字段偏好 {表名: 字段列表}
        """
        try:
            config = self._load_config_file()

            table_prefs = config.get('table_preferences', {})

            result = {}
            for table_name, preference_data in table_prefs.items():
                if isinstance(preference_data, dict):
                    result[table_name] = preference_data.get('preferred_fields', [])
                elif isinstance(preference_data, list):
                    # 兼容旧格式
                    result[table_name] = preference_data

            return result

        except Exception as e:
            self.logger.error(f"获取所有表级字段偏好失败: {e}")
            return {}

    def migrate_global_to_table_preferences(self, target_table_names: List[str]) -> bool:
        """将全局用户偏好迁移到表级偏好

        Args:
            target_table_names: 需要迁移的表名列表

        Returns:
            bool: 是否迁移成功
        """
        try:
            with self.cache_lock:
                config = self._load_config_file()

                # 获取全局用户偏好
                user_prefs = config.get('user_preferences', {})
                header_prefs = user_prefs.get('header_preferences', {})

                if not header_prefs:
                    self.logger.info("无全局用户偏好需要迁移")
                    return True

                # 确保table_preferences存在
                if 'table_preferences' not in config:
                    config['table_preferences'] = {}

                migrated_count = 0
                for table_name in target_table_names:
                    # 检查是否有全局偏好且表级偏好不存在
                    if table_name in header_prefs and table_name not in config['table_preferences']:
                        global_pref = header_prefs[table_name]
                        if isinstance(global_pref, dict):
                            fields = global_pref.get('selected_fields', [])
                        elif isinstance(global_pref, list):
                            fields = global_pref
                        else:
                            continue

                        # 迁移到表级偏好
                        config['table_preferences'][table_name] = {
                            "preferred_fields": fields,
                            "updated_at": datetime.now().isoformat(),
                            "field_count": len(fields),
                            "migrated_from": "global_preferences"
                        }
                        migrated_count += 1

                if migrated_count > 0:
                    config["last_updated"] = datetime.now().isoformat()
                    success = self._save_config_file(config)

                    if success:
                        self.logger.info(f"成功迁移 {migrated_count} 个表的偏好设置到表级配置")
                    return success
                else:
                    self.logger.info("无需迁移偏好设置")
                    return True

        except Exception as e:
            self.logger.error(f"迁移偏好设置失败: {e}")
            return False

    def _publish_config_change_event(self, table_name: str, mapping: Dict[str, str], action: str):
        """发布配置变更事件

        Args:
            table_name: 表名
            mapping: 字段映射
            action: 操作类型 (save, update, delete)
        """
        try:
            if self.event_bus is not None:
                event_data = {
                    "type": "config_mapping_changed",
                    "table_name": table_name,
                    "mapping": mapping,
                    "action": action,
                    "timestamp": datetime.now().isoformat()
                }

                self.event_bus.publish(Event(event_type="config_mapping_changed", data=event_data))
                self.logger.debug(f"发布配置变更事件: {table_name} - {action}")
            else:
                self.logger.debug("事件总线未初始化，跳过事件发布")

        except Exception as e:
            self.logger.warning(f"发布配置变更事件失败: {e}")

    def get_field_mapping_for_table(self, table_name: str) -> Optional[Dict[str, str]]:
        """获取指定表的字段映射配置（兼容方法）

        Args:
            table_name: 表名

        Returns:
            Optional[Dict[str, str]]: 字段映射字典 {英文字段名: 中文显示名}，如果不存在返回None
        """
        try:
            # 使用现有的load_mapping方法
            mapping = self.load_mapping(table_name)
            if mapping:
                self.logger.debug(f"🔧 [修复] 获取字段映射成功: {table_name}, {len(mapping)}个字段")
                return mapping
            else:
                self.logger.debug(f"🔧 [修复] 表 {table_name} 没有字段映射配置")
                return None
        except Exception as e:
            self.logger.error(f"🔧 [修复] 获取字段映射失败 {table_name}: {e}")
            return None

    def get_table_field_preference(self, table_name: str) -> Optional[List[str]]:
        """获取表级字段偏好设置（兼容方法）

        Args:
            table_name: 表名

        Returns:
            Optional[List[str]]: 用户偏好字段列表，如果不存在返回None
        """
        try:
            # 使用现有的get_user_header_preference方法
            preference = self.get_user_header_preference(table_name)
            if preference:
                self.logger.debug(f"🔧 [修复] 获取表级偏好成功: {table_name}, {len(preference)}个字段")
                return preference
            else:
                self.logger.debug(f"🔧 [修复] 表 {table_name} 没有用户偏好设置")
                return None
        except Exception as e:
            self.logger.error(f"🔧 [修复] 获取表级偏好失败 {table_name}: {e}")
            return None


# 🔧 [P1-4修复] 全局单例支持
import threading
from typing import Optional

_global_config_sync_manager: Optional[ConfigSyncManager] = None
_config_sync_manager_lock = threading.Lock()


def get_global_config_sync_manager(config_path: str = "state/data/field_mappings.json",
                                  event_bus=None) -> ConfigSyncManager:
    """
    🔧 [P1-4修复] 获取全局ConfigSyncManager单例实例

    Args:
        config_path: 配置文件路径
        event_bus: 事件总线实例

    Returns:
        ConfigSyncManager: 全局单例实例
    """
    global _global_config_sync_manager

    if _global_config_sync_manager is None:
        with _config_sync_manager_lock:
            if _global_config_sync_manager is None:
                _global_config_sync_manager = ConfigSyncManager(
                    config_path=config_path,
                    event_bus=event_bus
                )
                _global_config_sync_manager.logger.info("🔧 [P1-4修复] 创建全局ConfigSyncManager单例实例")

    return _global_config_sync_manager


def reset_global_config_sync_manager():
    """
    🔧 [P1-4修复] 重置全局ConfigSyncManager实例

    主要用于测试场景，清理全局状态。
    """
    global _global_config_sync_manager

    with _config_sync_manager_lock:
        if _global_config_sync_manager is not None:
            _global_config_sync_manager.logger.info("🔧 [P1-4修复] 重置全局ConfigSyncManager实例")
        _global_config_sync_manager = None