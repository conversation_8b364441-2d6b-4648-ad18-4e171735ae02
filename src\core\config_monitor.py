# -*- coding: utf-8 -*-
"""
配置文件监控器
用于监控配置文件变更并提供回调机制
"""

import time
import threading
import json
from typing import Dict, Any, Callable, List, Optional
from pathlib import Path
from src.modules.logging.setup_logger import setup_logger


class ConfigMonitor:
    """
    配置文件监控器

    监控配置文件的变更，并在变更时触发回调函数
    """

    def __init__(self, config_file_path: str):
        """
        初始化配置监控器

        Args:
            config_file_path: 配置文件路径
        """
        self.config_file_path = Path(config_file_path)
        self.last_modified = 0
        self.callbacks = []
        self.monitoring = False
        self.monitor_thread = None
        self.logger = setup_logger("ConfigMonitor")


        # 写入静默窗口：在此期间检测到变更也不触发回调（避免自身写入风暴）
        self._silent_until: float = 0.0

        # 初始化文件修改时间
        if self.config_file_path.exists():
            self.last_modified = self.config_file_path.stat().st_mtime

    def begin_silence(self, duration_seconds: float = 1.5):
        """进入静默窗口，忽略文件变更回调一段时间"""
        try:
            import time as _t
            self._silent_until = max(self._silent_until, _t.time() + max(0.1, float(duration_seconds)))
            self.logger.debug(f"🔔 [配置监控] 进入静默窗口 {duration_seconds}s")
        except Exception:
            pass

    def end_silence(self):
        """结束静默窗口"""
        self._silent_until = 0.0
        self.logger.debug("🔔 [配置监控] 结束静默窗口")

    def add_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """
        添加配置变更回调

        Args:
            callback: 回调函数，接收配置数据作为参数
        """
        self.callbacks.append(callback)
        self.logger.debug(f"🔔 [配置监控] 添加回调函数，当前回调数: {len(self.callbacks)}")

    def remove_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """
        移除配置变更回调

        Args:
            callback: 要移除的回调函数
        """
        if callback in self.callbacks:
            self.callbacks.remove(callback)
            self.logger.debug(f"🔔 [配置监控] 移除回调函数，当前回调数: {len(self.callbacks)}")

    def start_monitoring(self, check_interval: float = 1.0):
        """
        开始监控配置文件

        Args:
            check_interval: 检查间隔（秒）
        """
        if self.monitoring:
            self.logger.warning("🔔 [配置监控] 监控已在运行中")
            return

        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(check_interval,),
            daemon=True,
            name="ConfigMonitorThread"
        )
        self.monitor_thread.start()
        self.logger.info(f"🔔 [配置监控] 开始监控配置文件: {self.config_file_path}")

    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring:
            return

        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        self.logger.info("🔔 [配置监控] 停止监控")

    def _monitor_loop(self, check_interval: float):
        """
        监控循环

        Args:
            check_interval: 检查间隔
        """
        while self.monitoring:
            try:
                if self.config_file_path.exists():
                    current_modified = self.config_file_path.stat().st_mtime
                    if current_modified > self.last_modified:
                        # 检查是否处于静默窗口
                        now = time.time()
                        if now < getattr(self, '_silent_until', 0.0):
                            self.logger.debug("🔔 [配置监控] 处于静默窗口，忽略本次变更通知")
                        else:
                            self.logger.info("🔔 [配置监控] 检测到配置文件变更")
                            self.last_modified = current_modified
                            self._notify_callbacks()

                time.sleep(check_interval)

            except Exception as e:
                self.logger.error(f"🔔 [配置监控] 监控循环错误: {e}")
                time.sleep(check_interval)

    def _notify_callbacks(self):
        """通知所有回调"""
        try:
            # 加载配置内容
            config_data = self._load_config()

            # 通知所有回调
            for callback in self.callbacks:
                try:
                    callback(config_data)
                except Exception as e:
                    self.logger.error(f"🔔 [配置监控] 回调执行失败: {e}")

        except Exception as e:
            self.logger.error(f"🔔 [配置监控] 回调通知错误: {e}")

    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件内容

        Returns:
            配置数据字典
        """
        try:
            if not self.config_file_path.exists():
                return {}

            with open(self.config_file_path, 'r', encoding='utf-8') as f:
                return json.load(f)

        except Exception as e:
            self.logger.error(f"🔔 [配置监控] 加载配置文件失败: {e}")
            return {}

    def force_check(self):
        """强制检查配置文件变更"""
        try:
            if self.config_file_path.exists():
                current_modified = self.config_file_path.stat().st_mtime
                if current_modified != self.last_modified:
                    self.logger.info("🔔 [配置监控] 强制检查发现配置变更")
                    self.last_modified = current_modified
                    self._notify_callbacks()
                else:
                    self.logger.debug("🔔 [配置监控] 强制检查未发现变更")
        except Exception as e:
            self.logger.error(f"🔔 [配置监控] 强制检查失败: {e}")


class ConfigConsistencyMonitor:
    """配置一致性监控器"""

    def __init__(self, config_file_path: str):
        """
        初始化一致性监控器

        Args:
            config_file_path: 配置文件路径
        """
        self.config_file_path = Path(config_file_path)
        self.logger = setup_logger("ConfigConsistencyMonitor")
        self.consistency_rules = []
        self.violation_handlers = []

    def add_consistency_rule(self, rule: Callable[[Dict[str, Any]], bool], rule_name: str = None):
        """
        添加一致性规则

        Args:
            rule: 规则函数，返回True表示一致
            rule_name: 规则名称
        """
        rule_info = {
            'rule': rule,
            'name': rule_name or rule.__name__
        }
        self.consistency_rules.append(rule_info)
        self.logger.debug(f"🔍 [一致性监控] 添加规则: {rule_info['name']}")

    def check_field_type_consistency(self, table_name: str) -> List[str]:
        """
        检查字段类型一致性

        Args:
            table_name: 表名

        Returns:
            问题列表
        """
        issues = []
        try:
            config = self._load_table_config(table_name)
            if not config:
                return ["配置文件不存在或为空"]

            # 按字段类型分组
            type_groups = {}
            for field_name, field_config in config.items():
                field_type = field_config.get('field_type')
                if field_type not in type_groups:
                    type_groups[field_type] = []
                type_groups[field_type].append(field_name)

            # 检查每组的格式化结果一致性
            for field_type, fields in type_groups.items():
                if len(fields) > 1 and field_type:
                    formats = []
                    for field in fields:
                        test_value = "1000.0"
                        formatted = self._format_test_value(test_value, field_type)
                        formats.append(formatted)

                    # 检查格式是否一致
                    unique_formats = set(formats)
                    if len(unique_formats) > 1:
                        field_format_pairs = dict(zip(fields, formats))
                        issues.append(f"字段类型 {field_type} 的字段格式不一致: {field_format_pairs}")

            return issues

        except Exception as e:
            self.logger.error(f"🔍 [一致性监控] 检查字段类型一致性失败: {e}")
            return [f"检查失败: {e}"]

    def _load_table_config(self, table_name: str) -> Dict[str, Any]:
        """加载表配置"""
        try:
            if not self.config_file_path.exists():
                return {}

            with open(self.config_file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            table_mappings = config_data.get("table_mappings", {})
            if table_name in table_mappings:
                return table_mappings[table_name].get("field_configs", {})

            return {}

        except Exception as e:
            self.logger.error(f"🔍 [一致性监控] 加载表配置失败: {e}")
            return {}

    def _format_test_value(self, value: str, field_type: str) -> str:
        """格式化测试值"""
        try:
            from src.modules.data_import.formatting_engine import get_formatting_engine
            formatting_engine = get_formatting_engine()
            return formatting_engine.format_value(value, field_type)
        except Exception as e:
            self.logger.error(f"🔍 [一致性监控] 格式化测试值失败: {e}")
            return value


# 全局配置监控器实例
_global_config_monitor = None


def get_config_monitor(config_file_path: str = "state/data/field_mappings.json") -> ConfigMonitor:
    """
    获取全局配置监控器实例

    Args:
        config_file_path: 配置文件路径

    Returns:
        配置监控器实例
    """
    global _global_config_monitor
    if _global_config_monitor is None:
        _global_config_monitor = ConfigMonitor(config_file_path)
    return _global_config_monitor
