2025-09-07 22:04:54.566 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-09-07 22:04:54.567 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-09-07 22:04:54.568 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-09-07 22:04:54.568 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-09-07 22:04:54.569 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-09-07 22:04:54.570 | INFO     | src:<module>:20 | 月度工资异动处理系统 v2.0.0-refactored 初始化完成
2025-09-07 22:04:58.684 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-09-07 22:04:58.686 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-09-07 22:04:58.686 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-09-07 22:04:58.687 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-09-07 22:04:58.689 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-09-07 22:04:58.689 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-09-07 22:04:58.690 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-07 22:04:58.691 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-07 22:04:58.692 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-07 22:04:58.693 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-07 22:04:58.700 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-09-07 22:04:58.709 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-09-07 22:04:58.710 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-09-07 22:04:58.712 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-07 22:04:58.722 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-09-07 22:04:58.723 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-09-07 22:04:58.726 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-09-07 22:04:58.727 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-09-07 22:04:58.729 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-07 22:04:58.730 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-09-07 22:04:58.732 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-09-07 22:04:58.737 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-09-07 22:04:58.737 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-09-07 22:04:58.739 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11903 | 🔧 [P2-3] 错误恢复策略注册完成
2025-09-07 22:04:58.740 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-07 22:04:58.741 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11758 | 🔧 [P2-3] 错误处理机制设置完成
2025-09-07 22:04:58.742 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11796 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-09-07 22:04:58.972 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-09-07 22:04:58.973 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-09-07 22:04:58.976 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-07 22:04:58.996 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:150 | 🔧 [配置修复] 创建了完整的默认配置，包含基本字段映射和模板
2025-09-07 22:04:58.998 | INFO     | src.modules.data_import.config_sync_manager:__init__:82 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-09-07 22:04:59.003 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-07 22:04:59.005 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-07 22:04:59.006 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-09-07 22:04:59.008 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:154 | 使用已存在的配置文件: state\data\field_mappings.json
2025-09-07 22:04:59.011 | INFO     | src.modules.data_import.config_sync_manager:__init__:82 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-09-07 22:04:59.015 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-09-07 22:04:59.016 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-09-07 22:04:59.017 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-07 22:04:59.017 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-07 22:04:59.019 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-07 22:04:59.023 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-07 22:04:59.029 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-09-07 22:04:59.030 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 53.8ms
2025-09-07 22:04:59.065 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-09-07 22:04:59.070 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-09-07 22:04:59.072 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-09-07 22:04:59.073 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-09-07 22:04:59.074 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-09-07 22:04:59.075 | INFO     | src.gui.prototype.prototype_main_window:__init__:3650 | 🚀 性能管理器已集成
2025-09-07 22:04:59.077 | INFO     | src.gui.prototype.prototype_main_window:__init__:3652 | ✅ 新架构集成成功！
2025-09-07 22:04:59.082 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3765 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-09-07 22:04:59.087 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3730 | ✅ 新架构事件监听器设置完成
2025-09-07 22:04:59.088 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-09-07 22:04:59.089 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-09-07 22:04:59.090 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-09-07 22:04:59.391 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2726 | 菜单栏创建完成
2025-09-07 22:04:59.393 | INFO     | src.gui.prototype.prototype_main_window:__init__:2701 | 菜单栏管理器初始化完成
2025-09-07 22:04:59.396 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-09-07 22:04:59.397 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5458 | 管理器设置完成，包含增强版表头管理器
2025-09-07 22:04:59.397 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5463 | 🔧 开始应用窗口级Material Design样式...
2025-09-07 22:04:59.398 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-09-07 22:04:59.402 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-09-07 22:04:59.403 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5470 | ✅ 窗口级样式应用成功
2025-09-07 22:04:59.407 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5511 | ✅ 响应式样式监听设置完成
2025-09-07 22:04:59.412 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-07 22:04:59.415 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-07 22:04:59.416 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-07 22:04:59.433 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-09-07 22:04:59.437 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-09-07 22:04:59.451 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-07 22:04:59.457 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-07 22:04:59.459 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_execute_salary_data_load:1821 | 动态加载了 1 个月份的工资数据导航
2025-09-07 22:04:59.465 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-09-07 22:04:59.467 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-07 22:04:59.468 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 0个展开项
2025-09-07 22:04:59.470 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表']
2025-09-07 22:04:59.471 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-09-07 22:04:59.472 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-09-07 22:04:59.479 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-09-07 22:04:59.502 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-07 22:04:59.513 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-09-07 22:04:59.517 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-07 22:04:59.519 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-09-07 22:04:59.520 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-09-07 22:04:59.521 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-09-07 22:04:59.526 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-07 22:04:59.527 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2180 | 找到最新工资数据路径: 工资表 > 2025年 > 05月 > unknown
2025-09-07 22:04:59.534 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1562 | 🔧 [P1-2修复] 成功获取到最新路径
2025-09-07 22:04:59.816 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-09-07 22:04:59.818 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-09-07 22:04:59.822 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-09-07 22:04:59.822 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-09-07 22:04:59.849 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-09-07 22:04:59.850 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-09-07 22:04:59.855 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-09-07 22:04:59.856 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-09-07 22:04:59.858 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-09-07 22:04:59.859 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-07 22:04:59.860 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-07 22:04:59.861 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-09-07 22:04:59.877 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-09-07 22:04:59.881 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-09-07 22:04:59.883 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-09-07 22:04:59.884 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-09-07 22:04:59.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-09-07 22:04:59.886 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: False
2025-09-07 22:04:59.887 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-09-07 22:04:59.888 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-09-07 22:04:59.899 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-09-07 22:04:59.903 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-09-07 22:04:59.919 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-09-07 22:04:59.924 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-07 22:04:59.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-07 22:04:59.929 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-07 22:04:59.947 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-09-07 22:04:59.950 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-09-07 22:04:59.952 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-09-07 22:04:59.952 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-07 22:04:59.969 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-09-07 22:04:59.974 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-09-07 22:04:59.976 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-09-07 22:05:00.011 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-07 22:05:00.040 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-07 22:05:00.043 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-07 22:05:00.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-07 22:05:00.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-09-07 22:05:00.090 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-09-07 22:05:00.102 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 156.0ms
2025-09-07 22:05:00.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-07 22:05:00.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-07 22:05:00.106 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1710 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-09-07 22:05:00.108 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-07 22:05:00.120 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-07 22:05:00.153 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-09-07 22:05:00.185 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-09-07 22:05:00.193 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-09-07 22:05:00.251 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-09-07 22:05:00.308 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5420 | 快捷键设置完成
2025-09-07 22:05:00.308 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5377 | 主窗口UI设置完成。
2025-09-07 22:05:00.312 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5614 | 🔧 [全局排序] 全局排序开关连接成功
2025-09-07 22:05:00.314 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5646 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-09-07 22:05:00.315 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5658 | ✅ 已连接分页刷新信号到主窗口
2025-09-07 22:05:00.316 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页组件事件到新架构
2025-09-07 22:05:00.317 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5670 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-09-07 22:05:00.318 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5673 | 信号连接设置完成
2025-09-07 22:05:00.319 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6935 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-09-07 22:05:00.320 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6945 | ✅ [P1-2修复] 已加载字段映射信息，共0个表的映射
2025-09-07 22:05:00.342 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-07 22:05:00.343 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-07 22:05:00.344 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-07 22:05:00.345 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-07 22:05:00.346 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-09-07 22:05:00.353 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-09-07 22:05:00.354 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-07 22:05:00.359 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-07 22:05:00.365 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-07 22:05:00.366 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-07 22:05:00.367 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 21.5ms
2025-09-07 22:05:00.368 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-07 22:05:00.369 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-07 22:05:00.371 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1710 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-09-07 22:05:00.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-07 22:05:00.379 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-07 22:05:00.380 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-09-07 22:05:00.382 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8637 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-09-07 22:05:00.385 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-07 22:05:00.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-07 22:05:00.403 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-07 22:05:00.405 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-07 22:05:00.406 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-09-07 22:05:00.407 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-07 22:05:00.409 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-07 22:05:00.410 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-07 22:05:00.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-07 22:05:00.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 34.9ms
2025-09-07 22:05:00.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-07 22:05:00.441 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-07 22:05:00.442 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1710 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-09-07 22:05:00.443 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-07 22:05:00.444 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-07 22:05:00.449 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8655 | 已显示标准空表格，表头数量: 22
2025-09-07 22:05:00.452 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | 原型主窗口初始化完成
2025-09-07 22:05:00.627 | INFO     | __main__:main:514 | 应用程序启动成功
2025-09-07 22:05:00.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-07 22:05:00.636 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-09-07 22:05:00.636 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-09-07 22:05:00.640 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-07 22:05:00.640 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2180 | 找到最新工资数据路径: 工资表 > 2025年 > 05月 > unknown
2025-09-07 22:05:00.641 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1562 | 🔧 [P1-2修复] 成功获取到最新路径
2025-09-07 22:05:00.655 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > unknown', '工资表 > 2025年 > 05月', '工资表 > 2025年', '工资表']
2025-09-07 22:05:00.659 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7468 | 导航变化: 工资表 > 2025年 > 05月 > unknown
2025-09-07 22:05:00.659 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10628 | 🚫 [用户要求] 表格切换不显示加载条:  -> None
2025-09-07 22:05:00.665 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10644 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-07 22:05:00.677 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8436 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', 'unknown'] -> salary_data_2025_05_unknown_category
2025-09-07 22:05:00.686 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7530 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 工资表 > 2025年 > 05月 > unknown
2025-09-07 22:05:00.687 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-09-07 22:05:00.688 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:372 | 已清理表 salary_data_2025_05_unknown_category 的缓存
2025-09-07 22:05:00.692 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11314 | 已注册 2 个表格到表头管理器
2025-09-07 22:05:00.778 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-09-07 22:05:00.787 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 8.90ms
2025-09-07 22:05:00.788 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-09-07 22:05:00.789 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7689 | 🆕 使用新架构加载数据: salary_data_2025_05_unknown_category（通过事件系统）
2025-09-07 22:05:00.792 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 5 个匹配类型 'None' 的表 (尝试 1/5)
2025-09-07 22:05:00.792 | WARNING  | src.core.unified_data_request_manager:resolve_table_name:762 | 🔧 [P2修复] 表 salary_data_2025_05_unknown_category 不存在，使用相似表: salary_data_2025_05
2025-09-07 22:05:00.816 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-07 22:05:00.822 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 5 个匹配类型 'None' 的表 (尝试 1/5)
2025-09-07 22:05:00.823 | WARNING  | src.core.unified_data_request_manager:resolve_table_name:762 | 🔧 [P2修复] 表 salary_data_2025_05_unknown_category 不存在，使用相似表: salary_data_2025_05
2025-09-07 22:05:00.824 | INFO     | src.core.unified_data_request_manager:request_table_data:237 | 开始处理数据请求
2025-09-07 22:05:00.827 | INFO     | src.core.field_mapping_manager:_save_config:131 | 🔧 [P3优化] 字段映射配置已保存
2025-09-07 22:05:00.834 | INFO     | src.core.field_mapping_manager:_save_config:131 | 🔧 [P3优化] 字段映射配置已保存
2025-09-07 22:05:00.836 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:823 | 正在从表 salary_data_2025_05 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-09-07 22:05:00.846 | INFO     | src.core.sort_cache_manager:__init__:93 | SortCacheManager 初始化完成
2025-09-07 22:05:00.848 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:838 | 表 salary_data_2025_05 为空。
2025-09-07 22:05:00.851 | INFO     | src.core.unified_data_request_manager:request_table_data:287 | 数据请求处理完成: 字段=0, 行数=0, 耗时=27.5ms
2025-09-07 22:05:00.853 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_05, 变更类型: None
2025-09-07 22:05:00.854 | INFO     | src.services.table_data_service:load_table_data:528 | [修复数据发布] 数据加载成功，发布更新事件: 0行
2025-09-07 22:05:00.855 | INFO     | src.services.table_data_service:load_table_data:532 | 📨[data_event] publish | table=salary_data_2025_05 | rows=0 | page=1 | size=50 | total=0 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1757253900854-6ad81750
2025-09-07 22:05:00.855 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3822 | 📥[data_event] received | table=salary_data_2025_05 | request_id=SV-1757253900854-6ad81750
2025-09-07 22:05:00.856 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3854 | 数据内容: 0行 x 0列
2025-09-07 22:05:00.857 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3865 | 🔧 [分页修复] 从服务层获取总记录数: 0
2025-09-07 22:05:00.864 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3885 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_05, 0行
2025-09-07 22:05:00.928 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:389 | 已清理所有缓存
2025-09-07 22:05:00.945 | WARNING  | src.gui.prototype.prototype_main_window:_validate_data_state_sync:11175 | 表名状态不同步: 主窗口=salary_data_2025_05, 表格组件=salary_data_2025_05_unknown_category
2025-09-07 22:05:00.948 | WARNING  | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11124 | ⚠️ [新架构] 组件状态不一致: {'table_pagination_sync': True, 'data_state_sync': False, 'ui_component_sync': True, 'overall_consistency': False}
2025-09-07 22:05:00.950 | INFO     | src.gui.prototype.prototype_main_window:_auto_fix_state_inconsistency:11210 | 🔧 [新架构] 开始自动修复状态不一致
2025-09-07 22:05:00.950 | INFO     | src.gui.prototype.prototype_main_window:_fix_data_state_sync:11260 | 🔧 已修复数据状态同步: salary_data_2025_05
2025-09-07 22:05:00.951 | INFO     | src.gui.prototype.prototype_main_window:_auto_fix_state_inconsistency:11224 | 🔧 [新架构] 状态不一致自动修复完成
2025-09-07 22:05:00.952 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11085 | 🆕 [新架构] 导航迁移完成
2025-09-07 22:05:00.953 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4023 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-07 22:05:00.954 | INFO     | src.services.table_data_service:load_table_data:555 | 📨[data_event] published
2025-09-07 22:05:00.954 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7694 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-07 22:05:00.961 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-09-07 22:05:00.977 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-07 22:05:00.978 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2180 | 找到最新工资数据路径: 工资表 > 2025年 > 05月 > unknown
2025-09-07 22:05:00.979 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1562 | 🔧 [P1-2修复] 成功获取到最新路径
2025-09-07 22:05:00.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_05
2025-09-07 22:05:00.991 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_05
2025-09-07 22:05:01.061 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-09-07 22:05:01.066 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2079 | MainWorkspaceArea 响应式适配: sm
2025-09-07 22:05:01.575 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9539 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-09-07 22:05:01.577 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9449 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-09-07 22:05:01.584 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9463 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-09-07 22:05:01.585 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9997 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-09-07 22:05:01.602 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9469 | 🔧 [P0-1] 智能显示亮度修复完成
2025-09-07 22:05:03.370 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-09-07 22:05:03.370 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5906 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 05月 > unknown。打开导入对话框。
2025-09-07 22:05:03.658 | ERROR    | src.gui.prototype.prototype_main_window:_show_unified_import_dialog:6012 |  打开数据导入窗口失败: unexpected indent (unified_data_import_window.py, line 5559)
2025-09-07 22:05:06.787 | INFO     | __main__:main:519 | 应用程序正常退出
2025-09-07 22:05:06.793 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_0_2346150021744 已自动清理（弱引用回调）
